{% extends "be/include/base.html" %}

{% set page = 'EVENT' %}
{% set title = 'Eventi' %}

{% block extrahead %}
<title>Utenti</title>
{% include "be/include/snippets/plugins/datatable.html" %}
<script src="{{ contextPath }}/be/js/pages/event-collection.js?{{ buildNumber }}"></script>        
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
addRoute('BE_EVENT_DATA', '{{ routes("BE_EVENT_DATA") }}');
addRoute('BE_EVENT_OPERATE', '{{ routes("BE_EVENT_OPERATE") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Checkbox selection -->
    <div class="card">
            <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
                <h5 class="py-sm-3 mb-sm-0">{{ title }}</h5>
                <div class="ms-sm-auto my-sm-auto">
                    <a href="{{ routes('BE_EVENT') }}" class="btn btn-primary w-100">
                        <i class="ph-plus me-2"></i>
                        NUOVO EVENTO
                    </a>
                </div>
            </div>

        <table class="table datatable">
            <thead>
                <tr>                    
                    <th>Titolo</th>
                    <th>Categoria</th>                                        
                    <th>Luogo</th>                                        
                    <th>Data inserimento</th>                    
                    <th>Stato</th>                    
                    <th class="text-center">Azioni</th>
                </tr>
            </thead>
        </table>
    </div>
    <!-- /checkbox selection -->
</div>
<!-- /content area -->

{% endblock %}
/**
 * Wizard Steps Manager
 * Gestisce la funzionalità di un wizard a step con navigazione,
 * indicatori di progresso e controlli dei pulsanti.
 * 
 * Richiede: jquery-validate-config.js
 */
document.addEventListener('DOMContentLoaded', function() {
  const StepWizard = {
    /**
     * Elementi DOM utilizzati nel wizard
     */
    elements: {
      steps: document.querySelectorAll('.step-tab-panel'),
      navItems: document.querySelectorAll('.nat-item'),
      progressBar: document.querySelector('.progress-bar'),
      prevBtn: document.querySelector('.step-btn[data-step-action="prev"]'),
      nextBtn: document.querySelector('.step-btn[data-step-action="next"]'),
      finishBtn: document.querySelector('.step-btn[data-step-action="finish"]'),
      form: document.querySelector('.step-content') || document.querySelector('form')
    },
    
    /**
     * Configurazioni base del wizard
     */
    config: {
      currentStep: 1,
      totalSteps: 0,
      stepPrefix: 'step'
    },
    
    /**
     * Configurazione save
     */
    save: {
      enabled: false,
      saveUrl: null,
      businessId: null,
      isSaving: false,
      saveIndicator: null
    },
    
    /**
     * Istanza del validator jQuery
     */
    validator: null,
    
    /**
     * Inizializza il wizard
     */
    init: function() {
      // Conta il numero totale di step
      this.config.totalSteps = this.elements.steps.length;
      
      // Inizializza la validazione usando la configurazione globale
      this.initValidation();
      
      // Inizializza save se siamo nella pagina business edit
      this.initSave();
      
      // Controlla se c'è un parametro "step" nell'URL
      const urlParams = new URLSearchParams(window.location.search);
      const stepParam = urlParams.get('step');
      let initialStep = 1;
      
      if (stepParam) {
        const stepNumber = parseInt(stepParam, 10);
        // Verifica che il numero di step sia valido
        if (stepNumber >= 1 && stepNumber <= this.config.totalSteps) {
          initialStep = stepNumber;
        }
      }
      
      // Imposta lo step iniziale (da URL o default a 1)
      this.goToStep(initialStep);
      
      // Aggiungi gli eventi ai pulsanti di navigazione
      this.setupEventListeners();
    },
    
    /**
     * Inizializza jQuery Validate usando la configurazione globale
     */
    initValidation: function() {
      if (!this.elements.form) {
        console.warn('Form non trovato per la validazione');
        return;
      }
      
      // Usa la configurazione globale e inizializza il validator
      this.validator = $(this.elements.form).validate({
        // Ignora campi nascosti tranne quelli in step non attivi
        ignore: ':hidden:not(.choice-input)'
      });
      
      // Setup specifico per Choice.js nel wizard
      this.setupChoiceJsValidation();
    },
    
    /**
     * Setup validazione per Choice.js nel wizard
     */
    setupChoiceJsValidation: function() {
      // Trova tutti i select che potrebbero essere stati trasformati da Choice.js
      const choiceSelects = this.elements.form.querySelectorAll('select.choices__input, .choices select');
      
      choiceSelects.forEach(select => {
        const $select = $(select);
        
        // Aggiungi classe per identificazione
        $select.addClass('choice-input');
        
        // Listener per aggiornare la validazione quando cambia la selezione
        $select.on('change', function() {
          if (StepWizard.validator) {
            $(this).valid();
          }
        });
      });
    },
    
    /**
     * Configura i listener degli eventi
     */
    setupEventListeners: function() {
      // Evento per il pulsante Indietro
      if (this.elements.prevBtn) {
        this.elements.prevBtn.addEventListener('click', () => {
          this.goToStep(this.config.currentStep - 1);
        });
      }
      
      // Evento per il pulsante Avanti
      if (this.elements.nextBtn) {
        this.elements.nextBtn.addEventListener('click', () => {
          if (this.validateCurrentStep()) {
            this.goToStep(this.config.currentStep + 1);
          }
        });
      }
      
      // Evento per il pulsante Salva Attività
      if (this.elements.finishBtn) {
        this.elements.finishBtn.addEventListener('click', () => {
          if (this.validateCurrentStep()) {
            this.finishWizard();
          }
        });
      }
      
      // Aggiungi click event per la navigazione diretta
      this.elements.navItems.forEach((navItem, index) => {
        const link = navItem.querySelector('.nav-link');
        if (link) {
          link.addEventListener('click', () => {
            const targetStep = index + 1;
            // Permetti la navigazione diretta solo agli step già completati
            if (targetStep < this.config.currentStep) {
              this.goToStep(targetStep);
            }
          });
        }
      });
    },
    
    /**
     * Naviga a uno specifico step
     * @param {number} stepNumber - Il numero dello step di destinazione
     */
    goToStep: function(stepNumber) {
      // Controlla se lo step è valido
      if (stepNumber < 1 || stepNumber > this.config.totalSteps) {
        return;
      }
      
      // Salva prima di cambiare step (solo se non è il primo caricamento)
      if (this.save.enabled && this.config.currentStep !== stepNumber) {
        this.saveFormData();
      }
      
      // Aggiorna lo step corrente
      this.config.currentStep = stepNumber;
      
      // Nascondi tutti i pannelli e visualizza solo quello corrente
      this.elements.steps.forEach((step, index) => {
        const stepNum = index + 1;
        step.classList.toggle('active', stepNum === stepNumber);
      });            
      
      // Aggiorna i nav items della sidebar
      this.updateNavItems();
      
      // Aggiorna la barra di progresso
      this.updateProgressBar();
      
      // Aggiorna i pulsanti di navigazione
      this.updateNavigationButtons();
      
      // Reset eventuali errori di validazione dello step precedente
      if (this.validator) {
        this.validator.resetForm();
      }
    },
    
    /**
     * Valida i dati dello step corrente usando jQuery Validate
     * @returns {boolean} - True se la validazione è passata, altrimenti False
     */
    validateCurrentStep: function() {
      if (!this.validator) {
        console.warn('Validator non inizializzato, uso validazione base');
        return this.validateCurrentStepBasic();
      }
      
      // Ottieni il pannello dello step corrente
      const currentPanel = document.querySelector(`.step-tab-panel[data-step="${this.config.stepPrefix}${this.config.currentStep}"]`);
      if (!currentPanel) {
        console.warn('Pannello step corrente non trovato');
        return true;
      }
      
      // Trova tutti i campi required nello step corrente
      const requiredFields = currentPanel.querySelectorAll('[required]');
      let isValid = true;
      let firstInvalidField = null;
      
      // Valida ogni campo required
      requiredFields.forEach(field => {
        const $field = $(field);
        
        // Forza la validazione del campo
        const fieldValid = $field.valid();
        
        if (!fieldValid) {
          isValid = false;
          
          // Memorizza il primo campo con errore per il focus
          if (!firstInvalidField) {
            firstInvalidField = field;
          }
        }
      });
      
      // Controlla anche campi Choice.js
      const choiceFields = currentPanel.querySelectorAll('.choice-input[required]');
      choiceFields.forEach(field => {
        const $field = $(field);
        const fieldValid = $field.valid();
        
        if (!fieldValid) {
          isValid = false;
          if (!firstInvalidField) {
            firstInvalidField = field;
          }
        }
      });
      
      // Scroll al primo campo con errore
      if (!isValid && firstInvalidField) {
        setTimeout(() => {
          firstInvalidField.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'center' 
          });
          firstInvalidField.focus();
        }, 100);
      }
      
      return isValid;
    },
    
    /**
     * Validazione base di fallback
     */
    validateCurrentStepBasic: function() {
      const currentPanel = document.querySelector(`.step-tab-panel[data-step="${this.config.stepPrefix}${this.config.currentStep}"]`);
      
      if (currentPanel) {
        const requiredFields = currentPanel.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
          if (!field.value.trim()) {
            isValid = false;
            field.classList.add('is-invalid');
            
            field.addEventListener('input', function() {
              this.classList.remove('is-invalid');
            }, { once: true });
          } else {
            field.classList.remove('is-invalid');
          }
        });
        
        return isValid;
      }
      
      return true;
    },
    
    /**
     * Aggiorna lo stato degli elementi di navigazione nella sidebar
     */
    updateNavItems: function() {
      this.elements.navItems.forEach((navItem, index) => {
        const stepNum = index + 1;
        const link = navItem.querySelector('.nav-link');
        const icon = link ? link.querySelector('i') : null;
        
        if (link) {
          // Rimuovi le classi prima di aggiungere quelle corrette
          link.classList.remove('active', 'completed', 'disabled');
          
          if (stepNum === this.config.currentStep) {
            // Step corrente
            link.classList.add('active');
            link.classList.remove('pe-none');
            link.classList.remove('disabled');
            // Aggiorna l'icona per lo step corrente
            if (icon) {
              icon.className = 'fi-arrow-right-circle fs-lg me-2';
            }
          } else if (stepNum < this.config.currentStep) {
            // Step completati
            link.classList.add('completed');
            link.classList.remove('pe-none');
            link.classList.remove('disabled');
            // Cambia l'icona per gli step completati
            if (icon) {
              icon.className = 'fi-check-circle fs-lg me-2';
            }
          } else {
            // Step futuri
            link.classList.add('disabled');
            // Mantieni l'icona originale per gli step futuri
            if (icon) {
              icon.className = 'fi-circle fs-lg me-2';
            }
          }
        }
      });
    },
    
    /**
     * Aggiorna la percentuale della barra di progresso
     */
    updateProgressBar: function() {
      const progressPercent = ((this.config.currentStep - 1) / (this.config.totalSteps - 1)) * 100;
      
      // Aggiorna entrambe le barre di progresso per il tema chiaro e scuro
      const progressBars = document.querySelectorAll('.progress-bar');
      progressBars.forEach(bar => {
        bar.style.width = progressPercent + '%';
        bar.setAttribute('aria-valuenow', progressPercent);
      });
    },
    
    /**
     * Aggiorna la visibilità dei pulsanti di navigazione
     */
    updateNavigationButtons: function() {
      // Gestione del pulsante Indietro
      if (this.elements.prevBtn) {
        if (this.config.currentStep === 1) {
          this.elements.prevBtn.classList.add('d-none');
        } else {
          this.elements.prevBtn.classList.remove('d-none');
        }
      }
      
      // Gestione dei pulsanti Avanti e Salva attività
      if (this.elements.nextBtn && this.elements.finishBtn) {
        if (this.config.currentStep === this.config.totalSteps) {
          this.elements.nextBtn.classList.add('d-none');
          this.elements.finishBtn.classList.remove('d-none');
        } else {
          this.elements.nextBtn.classList.remove('d-none');
          this.elements.finishBtn.classList.add('d-none');
        }
      }
    },
    
    /**
     * Esegue azioni finali quando il wizard è completato
     */
    finishWizard: function() {
      // Valida tutto il form prima di finire
      if (this.validator && !this.validator.form()) {
        // Form non valido - le tue label personalizzate si attiveranno automaticamente
        return;
      }
      
      // Salva finale prima di completare
      if (this.save.enabled) {
        this.saveFormData();
      }
      
      // Qui puoi inserire codice per gestire la sottomissione finale
      console.log('Wizard completato con successo!');
      
      // Esempio: invio del form se il wizard è parte di un form
      const form = document.querySelector('form');
      if (form) {
        form.submit();
      }
      
      // Alternativamente, puoi raccogliere tutti i dati e inviarli tramite AJAX
      // this.submitDataViaAjax();
    },
    
    // ===== SAVE METHODS =====
    
    /**
     * Inizializza la funzionalità di save
     */
    initSave: function() {
      // Controlla se siamo nella pagina business edit
      if (!window.location.pathname.includes('business') || !window.location.pathname.includes('edit')) {
        return;
      }
      
      // Ottieni business ID dall'URL
      this.save.businessId = this.getBusinessIdFromUrl();
      if (!this.save.businessId) {
        console.warn('Business ID not found in URL. Save disabled.');
        return;
      }
      
      // Imposta URL di salvataggio
      this.save.saveUrl = $("#form-business-edit").attr("action");
      
      // Crea indicatore di salvataggio
      this.createSaveIndicator();
      
      this.save.enabled = true;
      console.log('Save inizializzato per business ID:', this.save.businessId);
    },
    
    /**
     * Ottieni business ID dall'URL
     */
    getBusinessIdFromUrl: function() {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get('oid');
    },
    
    /**
     * Ottieni context path
     */
    getContextPath: function() {
      const contextPath = document.querySelector('meta[name="context-path"]');
      if (contextPath) {
        return contextPath.getAttribute('content');
      }
      
      const path = window.location.pathname;
      const segments = path.split('/');
      return segments.length > 1 ? '/' + segments[1] : '';
    },
    
    /**
     * Ottieni lingua dall'URL
     */
    getLanguage: function() {
      const path = window.location.pathname;
      const segments = path.split('/');
      return segments.length > 2 ? segments[2] : 'it';
    },
    
    /**
     * Crea indicatore di salvataggio
     */
    createSaveIndicator: function() {
      const indicator = document.createElement('div');
      indicator.id = 'save-indicator';
      indicator.className = 'save-indicator';
      indicator.innerHTML = `
        <div class="save-status">
          <i class="fi-check-circle text-success d-none" id="save-success"></i>
          <i class="fi-clock text-warning d-none" id="save-pending"></i>
          <i class="fi-x-circle text-danger d-none" id="save-error"></i>
          <span class="save-text ms-2">Salvataggio</span>
        </div>
      `;
      
      // Aggiungi CSS
      const style = document.createElement('style');
      style.textContent = `
        .save-indicator {
          position: fixed;
          top: 20px;
          right: 20px;
          background: rgba(255, 255, 255, 0.95);
          border: 1px solid #dee2e6;
          border-radius: 8px;
          padding: 8px 12px;
          font-size: 14px;
          z-index: 1050;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
          transition: opacity 0.3s ease;
          opacity: 0;
          pointer-events: none;
        }
        .save-indicator.show {
          opacity: 1;
        }
        .save-status {
          display: flex;
          align-items: center;
        }
      `;
      document.head.appendChild(style);
      document.body.appendChild(indicator);
      
      this.save.saveIndicator = indicator;
    },
    
    /**
     * Mostra indicatore di salvataggio
     */
    showSaveIndicator: function(status, message) {
      if (!this.save.saveIndicator) return;
      
      const indicator = this.save.saveIndicator;
      const successIcon = indicator.querySelector('#save-success');
      const pendingIcon = indicator.querySelector('#save-pending');
      const errorIcon = indicator.querySelector('#save-error');
      const text = indicator.querySelector('.save-text');
      
      // Nascondi tutte le icone
      successIcon.classList.add('d-none');
      pendingIcon.classList.add('d-none');
      errorIcon.classList.add('d-none');
      
      // Mostra icona e messaggio appropriati
      switch (status) {
        case 'saving':
          pendingIcon.classList.remove('d-none');
          text.textContent = message || 'Salvataggio...';
          break;
        case 'success':
          successIcon.classList.remove('d-none');
          text.textContent = message || 'Salvato';
          break;
        case 'error':
          errorIcon.classList.remove('d-none');
          text.textContent = message || 'Errore salvataggio';
          break;
      }
      
      indicator.classList.add('show');
      
      // Auto-nascondi dopo 3 secondi per success/error
      if (status !== 'saving') {
        setTimeout(() => {
          indicator.classList.remove('show');
        }, 3000);
      }
    },
    
    /**
     * Salva dati del form via AJAX
     */
    saveFormData: function() {
      if (this.save.isSaving || !this.save.enabled) {
        return;
      }
      
      this.save.isSaving = true;
      this.showSaveIndicator('saving');
      
      // Raccogli dati del form
      const form = document.querySelector('.step-content');
      const formData = new FormData(form);
      
      // Controlla e converti gli imageIds in File
      for (let [key, value] of formData.entries()) {
        if (key.startsWith('imageIds-') && value) {
          try {
            const json = JSON.parse(value);
            const imageData = json.output.image;
            const fileName = json.output.name;
            const fileType = json.output.type;
            // Estrai i dati base64 rimuovendo l'header
            const base64Data = imageData.split(',')[1];
            const binaryData = atob(base64Data);
            // Converti i dati binari in un array di bytes
            const byteArray = new Uint8Array(binaryData.length);
            for (let i = 0; i < binaryData.length; i++) {
              byteArray[i] = binaryData.charCodeAt(i);
            }
            const file = new File([byteArray], fileName, {type: fileType});
            formData.set(key, file);
          } catch (e) {
            console.error('Error converting imageIds to File:', e);
          }
        }
      }
      
      if (!form) {
        console.error('Form non trovato');
        this.save.isSaving = false;
        this.showSaveIndicator('error', 'Form non trovato');
        return;
      }
      
      // Richiesta AJAX
      $.ajax({
        url: this.save.saveUrl,
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: (data) => {
          this.showSaveIndicator('success', 'Salvato');
          console.log('Business salvato con successo');
        },
        error: (error) => {
          console.error('Errore salvataggio:', error);
          this.showSaveIndicator('error', 'Errore salvataggio');
        },
        complete: () => {
          this.save.isSaving = false;
        }
      });
    }
  };
  
  // Inizializza il wizard quando jQuery è pronto
  $(document).ready(function() {
    StepWizard.init();
  });
});
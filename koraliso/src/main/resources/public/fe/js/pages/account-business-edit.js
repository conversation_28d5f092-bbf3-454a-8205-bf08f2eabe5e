var autocomplete, provinceCodeSelect;
$(function () {

    // leggere parametro "gallery" dall'url
    let searchParams = new URLSearchParams(window.location.search)
    let gallery = searchParams.get('gallery');
    if (gallery) {
        bindDropImage();
    }

    initAutocomplete();

    provinceCodeSelect = new Choices('#provinceCode', {
        classNames: {
            containerInner: ['form-select', 'form-select-lg']
        },
        searchEnabled: true
    });
});

function bindDropImage() {

    // ================================================================================
    // ================================================================================
    // ================================================================================
    // PQINA - SLIM MULTIPLE FILE UPLOAD
    // ================================================================================
    // ================================================================================
    // ================================================================================

    // 1. Handling the various events
    // - get references to different elements we need
    // - listen to drag, drop and change events
    // - handle dropped and selected files

    // get a reference to the file drop area and the file input
    var fileDropArea = document.querySelector('.image-std');
    if (fileDropArea) {
        var fileInput = fileDropArea.querySelector('input');
        var fileInputName = fileInput.name;

        // listen to events for dragging and dropping
        fileDropArea.addEventListener('dragover', handleDragOver);
        fileDropArea.addEventListener('drop', handleDrop);
        fileInput.addEventListener('change', handleFileSelect);

        // need to block dragover to allow drop
        function handleDragOver(e) {
            e.preventDefault();
        }

        // deal with dropped items,
        function handleDrop(e) {
            e.preventDefault();
            handleFileItems(e.dataTransfer.items || e.dataTransfer.files);
        }

        // handle manual selection of files using the file input
        function handleFileSelect(e) {
            handleFileItems(e.target.files);
        }

        // 2. Handle the dropped items
        // - test if the item is a File or a DataTransferItem
        // - do some expectation matching

        // loops over a list of items and passes
        // them to the next function for handling
        function handleFileItems(items) {
            var l = items.length;
            for (var i = 0; i < l; i++) {
                handleItem(items[i]);
            }
        }

        // handles the dropped item, could be a DataTransferItem
        // so we turn all items into files for easier handling
        function handleItem(item) {

            // get file from item
            var file = item;
            if (item.getAsFile && item.kind == 'file') {
                file = item.getAsFile();
            }

            handleFile(file);
        }

        // now we're sure each item is a file
        // the function below can test if the files match
        // our expectations
        function handleFile(file) {

            /*
            // you can check if the file fits all requirements here
            // for example:
            // if file is bigger then 1 MB don't load
            if (file.size > 1000000) {
              return;
            }
            */

            // if it does, create a cropper
            createCropper(file);
        }

        // 3. Create the Image Cropper
        // - create an element for the cropper to bind to
        // - add the element after the drop area
        // - creat the cropper and bind the remove button so it
        //   removes the entire cropper when clicked.

        var progr = 0;

        // create an Image Cropper for each passed file
        function createCropper(file) {

            // create container element for cropper
            var cropper = document.createElement('div');
            cropper.classList.add('col-xs-6');
            cropper.classList.add('col-sm-3');
            cropper.classList.add('control-label');
            cropper.classList.add('slim-item-multiple');

            // insert this element after as the last element in drop area
            //var sibling = fileDropArea.nextSibling;
            var sibling = null;
            fileDropArea.parentNode.insertBefore(cropper, sibling);

            // let input name be unique on submit
            progr += 1;

            // create a Slim Cropper
            Slim.create(cropper, {
                maxFileSize: 10,
                saveInitialImage: false,
                push: false,
                post: "output",
                label: "Carica un'immagine",
                jpegCompression: 70,
                labelLoading: " ",
                buttonEditLabel: "Modifica",
                buttonRemoveLabel: "Elimina",
                buttonDownloadLabel: "Scarica",
                buttonUploadLabel: "Carica",
                buttonRotateLabel: "Ruota",
                buttonCancelLabel: "Cancella",
                buttonConfirmLabel: "Conferma",
                buttonEditTitle: "Modifica",
                buttonRemoveTitle: "Elimina",
                buttonDownloadTitle: "Scarica",
                buttonUploadTitle: "Carica",
                buttonRotateTitle: "Ruota",
                buttonCancelTitle: "Cancella",
                buttonConfirmTitle: "Conferma",
                statusFileSize: "Il file è troppo grande, il massimo consentito è $0 MB",
                statusFileType: "Formato immagine non valido, formati consentiti: $0",
                statusNoSupport: "Il tuo browser non supporta la modifica dell'immagine",
                statusImageTooSmall: "Immagine troppo piccola, risoluzione minima: $0 pixel",
                statusContentLength: "Il server non supporta file così grandi",
                statusUnknownResponse: "Errore sconosciuto, <NAME_EMAIL>",
                statusUploadSuccess: "Immagine salvata",
                //ratio: '9:16',
                ratio: 'free',
                defaultInputName: fileInputName + '-' + progr,
                didInit: function () {

                    // load the file to our slim cropper
                    this.load(file);

                },
                didRemove: function () {

                    // detach from DOM
                    cropper.parentNode.removeChild(cropper);

                    // destroy the slim cropper
                    this.destroy();

                }
            });

        }

        // 4. Disable the file input element

        // hide file input, we can now upload with JavaScript
        fileInput.style.display = 'none';

        // remove file input name so it's value is
        // not posted to the server
        fileInput.removeAttribute('name');

        // 5. Load pre-exisisting images
        var imageIdsText = $('#loadedImageIds').text();
        if (imageIdsText) {
            var imageIds = imageIdsText.trim().split('|');
            for (var i in imageIds) {
                if (imageIds[i]) {
                    var src = $("#imageUri").attr("href") + imageIds[i];
                    console.log('sto caricando ' + src);
                    createCropper(src);
                }
            }
        }

        // 6. Let area be a sortable zone
        Sortable.create(fileDropArea.parentNode, { /* options */});
    }
}

// async registering autocomplete address
function initAutocomplete() {
    (function () {
        var input = $('#fulladdress')[0];
        var options = {
            offset: 2
        };
        autocomplete = new google.maps.places.Autocomplete(input, options);
        autocomplete.addListener('place_changed', selectAddress);

        // hacking google logo
        setTimeout(function () {
            $('.pac-container').removeClass('pac-logo');
        }, 1000);
    })();
}

function selectAddress() {
    (function () {
        // get city
        var city = '';
        var address = '';
        var number = '';
        var postalCode = '';
        var countryCode = '';
        var provinceCode = '';
        var venue = '';
        var place = autocomplete.getPlace();
        if (place !== null) {
            if (typeof place.address_components !== 'undefined') {
                for (var i = 0; i < place.address_components.length; i++) {
                    var type = place.address_components[i].types[0];
                    if (type === 'locality') {
                        city = place.address_components[i]['long_name'];
                    }
                    if (type === 'route') {
                        address = place.address_components[i]['long_name'];
                    }
                    if (type === 'street_number') {
                        number = place.address_components[i]['long_name'];
                    }
                    if (type === 'postal_code') {
                        postalCode = place.address_components[i]['long_name'];
                    }
                    if (type === 'administrative_area_level_2') {
                        provinceCode = place.address_components[i]['short_name'];
                    }
                    if (type === 'country') {
                        countryCode = place.address_components[i]['short_name'];
                    }
                    //                  type for venue
                    //                "point_of_interest"
                    //                "establishment"
                    if (place.types.includes("point_of_interest") || place.types.includes("establishment")) {
                        venue = place.name;
                    }
                }
                if (address !== '') {
                    if (number !== '') {
                        address += ' ' + number;
                    }
                }
            }
        }

        $('#countryCode').val(countryCode).change();
        checkCountryCode();
        $('#city').val(city);
        $('#address').val(address);
        $('#zip').val(postalCode);
        provinceCodeSelect.setChoiceByValue(provinceCode);
    })();
}

function checkCountryCode() {
    var countryCodeValue = $('#countryCode').val();
    if (countryCodeValue !== 'IT') {
        $('#provinceDiv').hide();
        $('#provinceExtDiv').show();
        $('.provinceCodeIt').prop('name', 'provinceCodeIt').prop('id', 'provinceCodeIt');
        $('.provinceCodeExt').prop('name', 'provinceCode').prop('id', 'provinceCode');
    } else {
        $('#provinceDiv').show();
        $('#provinceExtDiv').hide();
        $('.provinceCodeIt').prop('name', 'provinceCode').prop('id', 'provinceCode');
        $('.provinceCodeExt').prop('name', 'provinceCodeExt').prop('id', 'provinceCodeExt');
    }
    $('.provinceCode').val('');
}
package controller;

import static controller.LoginController.isPasswordCorrect;
import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.UserDao;
import enums.ProfileType;
import extensions.LabelsFunction;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.User;
import spark.*;
import utils.PasswordHash;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class AccountController {
 
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class.getName());
    
    public static TemplateViewRoute account_login = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        Core.initializeRouteFrontEnd(request, response, attributes);

        if (params.containsKey("wrongUsernamePassword")) {
            attributes.put("wrongUsernamePassword", true);
        }

        return Core.render(Pages.ACCOUNT_LOGIN, attributes, request);
    };
    
    public static Route login_do = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        String language = RoutesUtils.language(request);
        String username = params.get("username");
        String password = params.get("password");
        User user = UserDao.loadUserByEmail(username);
        if (user != null) {
            // check password
            if (isPasswordCorrect(user, password)) {

                String token = PasswordHash.getCookieSafeSecureRandom(32);
                Core.createSession(request, response, token);
                Core.addValueToSession(token, "user", user);
                return RoutesUtils.getLocalizedFullPath(request, "HOME", language);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.invalid.password"));
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.user.not.register"));
        }
        
    };
    
    public static TemplateViewRoute account_register = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.ACCOUNT_REGISTER, attributes, request);
    };
    
    public static TemplateViewRoute account_recover = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.ACCOUNT_RECOVER, attributes, request);
    };
    
    public static TemplateViewRoute account_verify = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        Core.initializeRouteFrontEnd(request, response, attributes);
        // todo mostrare email a cui è stata inviata email conferma
        return Core.render(Pages.ACCOUNT_VERIFY, attributes, request);
    };
        
    public static Route recover_send = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        
        return null;
    };  
    
    public static Route register_do = (Request request, Response response) -> {
        Map<String, UploadedFile> files = new HashMap<>();
        Map<String, String> params = new HashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String language = RoutesUtils.language(request);
        User newUser = RequestUtils.createFromParams(params, User.class);
        newUser.setPassword(PasswordHash.createHash(newUser.getPassword()));
        newUser.setUsername(newUser.getEmail());
        newUser.setProfileType(ProfileType.UNCONFIRMED.name());
        // check if user already exists
        User existingUser = UserDao.loadUserByEmail(newUser.getEmail());
        if (existingUser != null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.unable.to.update"));
        }

        ObjectId insertedId = UserDao.insertDocument(newUser);
        if (insertedId != null) {
            return RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_VERIFY", language);
        } else {
            return RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_REGISTER", language);
        }
    };

    public static TemplateViewRoute account_info = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);
        return Core.render(Pages.ACCOUNT_INFO, attributes, request);
    };

    public static Route account_info_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);

        if (user != null) {
            String language = RoutesUtils.language(request);
            Map<String, UploadedFile> files = new HashMap<>();
            Map<String, String> params = new HashMap<>();
            RequestUtils.parseRequest(request, params, files);

            user = RequestUtils.mergeFromParams(params, user);
            // update user on redis
            String token = Core.getSessionToken(request);
            Core.addValueToSession(token, "user", user);
            BaseDao.updateDocument(user);
            return RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_INFO", language);
        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };

    public static Route account_password_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);

        if (user != null) {
            String language = RoutesUtils.language(request);
            Map<String, UploadedFile> files = new HashMap<>();
            Map<String, String> params = new HashMap<>();
            RequestUtils.parseRequest(request, params, files);

            String currentPassword = params.get("currentPassword");
            String newPassword = params.get("newPassword");
            String newPasswordConfirmation = params.get("newPasswordConfirmation");

            if (!isPasswordCorrect(user, currentPassword)) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.invalid.password"));
            }

            if (!newPassword.equals(newPasswordConfirmation)) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.password.mismatch"));
            }

            user.setPassword(PasswordHash.createHash(newPassword));
            // update user on redis
            String token = Core.getSessionToken(request);
            Core.addValueToSession(token, "user", user);
            BaseDao.updateDocument(user);
            return RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_INFO", language);
        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };

    public static Route account_delete = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);

        if (user != null) {
            String language = RoutesUtils.language(request);
            Map<String, UploadedFile> files = new HashMap<>();
            Map<String, String> params = new HashMap<>();
            RequestUtils.parseRequest(request, params, files);

            String confirmDeletion = params.get("confirmDeletion");
            if (confirmDeletion == null) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.confirm.deletion"));
            }
            if (!confirmDeletion.equals("on")) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.confirm.deletion"));
            }

            user.setCancelled(true);
            BaseDao.updateDocument(user);
            Core.destroySession(request, response);
            return RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_REGISTER", language);
        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };

    public static Route account_image_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);

        if (user != null) {
            String language = RoutesUtils.language(request);
            Map<String, UploadedFile> files = new HashMap<>();
            Map<String, String> params = new HashMap<>();
            RequestUtils.parseRequest(request, params, files);

            if (!files.isEmpty()) {
                BaseDao.deleteImage(user, "imageId");
                BaseDao.saveImage(files.entrySet().iterator().next().getValue(), user, "imageId", true);

                String token = Core.getSessionToken(request);
                Core.addValueToSession(token, "user", user);
                BaseDao.updateDocument(user);
                return RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_INFO", language);
            }

            throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.image.not.found"));
        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };

    public static TemplateViewRoute account_favourites = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.ACCOUNT_FAVOURITES, attributes, request);
    };

    public static TemplateViewRoute account_businesses = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.ACCOUNT_BUSINESSES, attributes, request);
    };

    public static TemplateViewRoute account_business_info = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.ACCOUNT_BUSINESS_INFO, attributes, request);
    };

    public static TemplateViewRoute account_business_services = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.ACCOUNT_BUSINESS_SERVICES, attributes, request);
    };

    public static TemplateViewRoute account_business_bookings = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.ACCOUNT_BUSINESS_BOOKINGS, attributes, request);
    };

    public static TemplateViewRoute account_business_reviews = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.ACCOUNT_BUSINESS_REVIEWS, attributes, request);
    };

}
